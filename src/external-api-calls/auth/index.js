const makeAuth = require('./auth')
const makeAuthCallback = require('./authCallback')
const {OAuth2Client} = require('google-auth-library')

const CLIENT_ID = "288967092794-h0ia1acj2q18usektrk17r0dgj388cs6.apps.googleusercontent.com";
const CLIENT_SECRET = "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";
const REDIRECT_URI = "http://localhost:8080/auth/callback";

const client = new OAuth2Client(CLIENT_ID,CLIENT_SECRET,REDIRECT_URI)
console.log("client"+JSON.stringify(client));
const auth = makeAuth.makeAuth({client})
const authCallback = makeAuthCallback.makeAuthCallback({client})
module.exports = Object.freeze({
    auth,
    authCallback
})