const { Kafka } = require('kafkajs')

const kafka = new Kafka({
    clientId: 'my-app',
    brokers: ['localhost:9092']
  });
  
  const producer = kafka.producer()

module.exports = function makeCreateUserController({
    Joi,
    createUser,
    userExist,
    getIdByEmail,
    defaultFolders,
}) {
    return async function createUserController(req, res) {
        const database = req.headers['database'];
        console.info(`In create user controller`, req.body);
        const email = req.body.email;
        const name = req.body.name;
        const password = req.body.password;
        const accessToken = req.body.accessToken;
        const refreshToken = req.body.refreshToken;
        const expiry = req.body.expiry;
        console.log(expiry+"getting it expiry date in this format");
        const validateInput = validateUserInput({ Joi });

        try {
            validateInput(email, name, password);
        } catch (error) {
            res.status(400).send(error.message);
            return;
        }
        const ans = await userExist({ email,database });
        console.log(ans);
        if (ans == true) {
            console.log("EXIST")
            res.status(400).send('User Already Exist');
        }
        else {
            console.log("NOT EXIST");
            const re = await createUser({ email, name, password ,database,accessToken,refreshToken,expiry});
            console.log(re);
            const id = await getIdByEmail({email,database});
            console.log(id+"ID GETTING IN PRODUCER")
            await defaultFolders({id,database});
            const data = {
                userId:id,
                accessToken:accessToken,
                database:database
            }
        await producer.connect();
        console.log("producer connected successfully");
        await producer.send({
          topic: 'folders',
          messages: [
            {
              value: JSON.stringify({
              result: data,
              }),
            },
          ],
        });
        console.log("message send successfully");
        await producer.disconnect();
            res.status(200).send("USER SUCCESSFULLY CREATED");
        }
    }
}

function validateUserInput({ Joi }) {
    return function (email, name, password) {
        const schema = Joi.object({
            email: Joi.string().email().required(),
            name: Joi.string().required(),
            password: Joi.string().required(),
        });

        const { error, value } = schema.validate({ email, name, password});
        if (error) {
            throw new Error(error.details[0].message);
        }
    }
}