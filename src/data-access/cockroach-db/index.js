const CONFIG = require('../../config');
const fs = require('fs');
const { Pool } = require('pg');
const users_query = require('./User_query');
const folder_query = require('./folder_query')
const accessTokenUpdate = require('./userAcessTokenUpdate')

const db = new Pool({
  user: CONFIG.cockroach1.user.split(':')[0],
  host: CONFIG.cockroach1.host,
  password: CONFIG.cockroach1.user.split(':')[1],
  port: CONFIG.cockroach1.port,
  // ssl: {
  //   ca: CONFIG.cockroach1.ssl.ca,
  //   cert:CONFIG.cockroach1.ssl.cert ,
  //   key: CONFIG.cockroach1.ssl.key,
  //   rejectUnauthorized: false // This line is added to ignore self-signed certificates
  // }
});
db.connect((err, client, done) => {
  if (err) throw err;
  console.log('CockroachDB connected successfully!');
});

const Users = users_query({db});
const folders = folder_query({db});
// const accessToken = accessTokenUpdate({db});
// console.log(accessToken);
// accessToken();

module.exports = Object.freeze({
  Users,
  folders,
});
